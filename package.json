{"name": "google-places-lead-generator", "version": "1.0.0", "description": "A TypeScript application for generating business leads using Google Places API", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"typescript": "^5.3.0"}, "devDependencies": {"@types/node": "^20.10.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.0"}, "keywords": ["google-places", "lead-generation", "typescript", "business-search"], "author": "", "license": "MIT"}