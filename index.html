<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PressureWash Pro - Lead Generator</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Geist:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyB-kl4FwEBDnULVS-5FzgqmOILaHs5AETc&libraries=places"></script>
</head>
<body class="bg-black text-white font-geist min-h-screen">

    <div class="container mx-auto p-4 sm:p-6 lg:p-8">
        <header class="text-center mb-8">
            <div class="inline-flex items-center gap-3 mb-4">
                <div class="w-12 h-12 bg-blue-gradient rounded-lg flex items-center justify-center glow-blue">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <h1 class="text-3xl sm:text-4xl font-semibold text-white">PressureWash Pro Lead Generator</h1>
            </div>
            <p class="mt-2 text-neutral-400">Find pressure washing business leads by location across the US.</p>
        </header>


        <!-- Search Form -->
        <div class="bg-neutral-900 border border-neutral-800 p-6 rounded-lg mb-8">
            <form id="search-form" class="flex flex-col sm:flex-row sm:items-end gap-4">
                <div class="flex-grow">
                    <label for="search-query" class="block text-sm font-medium text-white mb-2">Search Query</label>
                    <div class="relative">
                        <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 12.414a5.5 5.5 0 10-1.414 1.414l4.243 4.243a1 1 0 001.414-1.414z"></path>
                        </svg>
                        <input type="text" id="search-query" name="search-query" class="w-full pl-10 pr-4 py-3 bg-neutral-800 border border-neutral-700 rounded-lg text-white placeholder:text-neutral-500 focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-colors" placeholder="e.g., Pressure Washing Phoenix AZ" required>
                    </div>
                </div>
                <div class="flex-shrink-0">
                    <label for="max-results" class="block text-sm font-medium text-white mb-2">Max Results</label>
                    <div class="flex gap-2">
                        <select id="max-results" name="max-results" class="px-4 py-3 bg-neutral-800 border border-neutral-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-colors">
                            <option value="20">20</option>
                            <option value="50">50</option>
                            <option value="100" selected>100</option>
                            <option value="200">200</option>
                            <option value="500">500</option>
                            <option value="custom">Custom</option>
                        </select>
                        <input type="number" id="custom-max-results" name="custom-max-results" class="hidden w-20 px-3 py-3 bg-neutral-800 border border-neutral-700 rounded-lg text-white placeholder:text-neutral-500 focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-colors" placeholder="100" min="1" max="1000">
                    </div>
                </div>
                <div class="flex-shrink-0">
                    <label class="flex items-center gap-2 text-sm text-white">
                        <input type="checkbox" id="fetch-details" name="fetch-details" checked class="rounded border-neutral-700 bg-neutral-800 text-blue-500 focus:ring-blue-500/50">
                        <span>Fetch contact details & reviews</span>
                    </label>
                    <p class="text-xs text-neutral-500 mt-1">Slower but includes phone, website, reviews</p>
                </div>
                <button type="submit" class="bg-blue-gradient-hover text-white font-medium px-8 py-3 rounded-lg shadow-lg transition-all duration-200 hover:transform hover:-translate-y-0.5">
                    <span class="flex items-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search
                    </span>
                </button>
            </form>
        </div>

        <!-- Controls & Results -->
        <main>
            <!-- Loading and Message Area -->
            <div id="status-area" class="text-center my-8"></div>

            <!-- Export Buttons -->
            <div id="export-controls" class="text-center mb-8 hidden">
                <div class="bg-neutral-900 border border-neutral-800 p-6 rounded-lg">
                    <h2 class="text-xl font-medium text-white mb-4">Search Results</h2>
                    <div class="flex justify-center gap-4">
                        <button id="export-csv" class="bg-green-500/10 border border-green-500/20 text-green-400 font-medium px-6 py-3 rounded-lg hover:bg-green-500/20 hover:text-green-300 transition-colors duration-200">
                            <span class="flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Export to CSV
                            </span>
                        </button>
                        <button id="export-json" class="bg-blue-500/10 border border-blue-500/20 text-blue-400 font-medium px-6 py-3 rounded-lg hover:bg-blue-500/20 hover:text-blue-300 transition-colors duration-200">
                            <span class="flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                                Export to JSON
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Results Grid -->
            <div id="results-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Results will be injected here -->
            </div>
        </main>
    </div>

    <script type="module" src="/src/main.ts"></script>
</body>
</html>
