<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Places Lead Generator</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyB-kl4FwEBDnULVS-5FzgqmOILaHs5AETc&libraries=places"></script>
</head>
<body class="bg-gray-100 text-gray-800 font-inter">

    <div class="container mx-auto p-4 sm:p-6 lg:p-8">
        <header class="text-center mb-8">
            <h1 class="text-3xl sm:text-4xl font-bold text-gray-900">Google Places Lead Generator</h1>
            <p class="mt-2 text-gray-600">Find business leads by type and location in the US.</p>
        </header>

        <!-- API Key Warning -->
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 rounded-md mb-6" role="alert">
            <p class="font-bold">Important: API Key Setup</p>
            <p>This application uses the Google Maps JavaScript API with Places Library. Make sure to <a href="https://console.cloud.google.com/" target="_blank" rel="noopener noreferrer" class="underline font-medium">enable the Maps JavaScript API and Places API</a> in your Google Cloud Console and restrict your API key with proper HTTP referrer restrictions (e.g., `http://localhost:3000/*` for local development).</p>
        </div>

        <!-- Search Form -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-8">
            <form id="search-form" class="flex flex-col sm:flex-row sm:items-end gap-4">
                <div class="flex-grow">
                    <label for="search-query" class="block text-sm font-medium text-gray-700 mb-1">Search Query</label>
                    <input type="text" id="search-query" name="search-query" class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500" placeholder="e.g., Pressure Washing Phoenix AZ">
                </div>
                <button type="submit" class="bg-indigo-600 text-white font-semibold px-6 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    Search
                </button>
            </form>
        </div>

        <!-- Controls & Results -->
        <main>
            <!-- Loading and Message Area -->
            <div id="status-area" class="text-center my-8"></div>

            <!-- Export Buttons -->
            <div id="export-controls" class="text-center mb-8 hidden">
                <h2 class="text-xl font-semibold mb-4">Search Results</h2>
                <div class="flex justify-center gap-4">
                    <button id="export-csv" class="bg-green-600 text-white font-semibold px-5 py-2 rounded-md hover:bg-green-700 transition-colors duration-200">Export to CSV</button>
                    <button id="export-json" class="bg-blue-600 text-white font-semibold px-5 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200">Export to JSON</button>
                </div>
            </div>

            <!-- Results Grid -->
            <div id="results-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Results will be injected here -->
            </div>
        </main>
    </div>

    <script type="module" src="/src/main.ts"></script>
</body>
</html>
