// Google Places API Types
export interface PlaceGeometry {
  location: {
    lat: number;
    lng: number;
  };
  viewport?: {
    northeast: {
      lat: number;
      lng: number;
    };
    southwest: {
      lat: number;
      lng: number;
    };
  };
}

export interface PlaceOpeningHours {
  open_now: boolean;
  periods?: Array<{
    close?: {
      day: number;
      time: string;
    };
    open: {
      day: number;
      time: string;
    };
  }>;
  weekday_text?: string[];
}

export interface PlacePhoto {
  height: number;
  html_attributions: string[];
  photo_reference: string;
  width: number;
}

export interface Place {
  business_status?: string;
  formatted_address?: string;
  geometry: PlaceGeometry;
  icon?: string;
  icon_background_color?: string;
  icon_mask_base_uri?: string;
  name: string;
  opening_hours?: PlaceOpeningHours;
  photos?: PlacePhoto[];
  place_id: string;
  plus_code?: {
    compound_code: string;
    global_code: string;
  };
  price_level?: number;
  rating?: number;
  reference?: string;
  types: string[];
  user_ratings_total?: number;
}

export interface PlacesSearchResponse {
  html_attributions: string[];
  next_page_token?: string;
  results: Place[];
  status: 'OK' | 'ZERO_RESULTS' | 'OVER_QUERY_LIMIT' | 'REQUEST_DENIED' | 'INVALID_REQUEST';
  error_message?: string;
}

// Application Types
export interface SearchFormData {
  query: string;
}

export interface ExportData {
  name: string;
  address: string;
  rating: number;
  totalRatings: number;
  status: string;
  placeId: string;
  types: string[];
}

export type MessageType = 'info' | 'error' | 'success';

export interface AppConfig {
  apiKey: string;
  placesApiUrl: string;
}
