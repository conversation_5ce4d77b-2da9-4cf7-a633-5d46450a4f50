@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for PressureWash Pro Design System */
:root {
  --background: 0 0% 0%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 217 91% 60%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 14.9%;
  --input: 0 0% 14.9%;
  --ring: 217 91% 60%;
  --blue-gradient-start: 217 91% 70%;
  --blue-gradient-end: 217 91% 45%;
}

/* Custom styles */
body {
  font-family: '<PERSON>eist', -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}

/* PressureWash Pro Design System Components */

/* Blue Gradient */
.bg-blue-gradient {
  background: linear-gradient(135deg, hsl(var(--blue-gradient-start)), hsl(var(--blue-gradient-end)));
}

.bg-blue-gradient-hover {
  background: linear-gradient(135deg, hsl(var(--blue-gradient-start)), hsl(var(--blue-gradient-end)));
  transition: all 0.3s ease;
}

.bg-blue-gradient-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px -5px hsl(var(--primary) / 0.3);
}

/* Card Hover Effects */
.card-hover-blue {
  transition: all 0.3s ease;
}

.card-hover-blue:hover {
  border-color: hsl(var(--primary) / 0.4);
  box-shadow: 0 8px 25px -5px hsl(var(--primary) / 0.1);
}

/* Glow Effects */
.glow-blue {
  box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
}

.glow-blue-strong {
  box-shadow: 0 0 30px hsl(var(--primary) / 0.5);
}

/* Loading spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Custom loader styles */
.loader {
  border: 4px solid hsl(var(--muted));
  border-radius: 50%;
  border-top: 4px solid hsl(var(--primary));
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(0 0% 10%);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, hsl(var(--blue-gradient-start)), hsl(var(--blue-gradient-end)));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, hsl(var(--blue-gradient-start) / 0.8), hsl(var(--blue-gradient-end) / 0.8));
}

/* Focus styles for better accessibility */
.focus\:ring-blue-500:focus {
  --tw-ring-color: hsl(var(--primary) / 0.5);
}

.focus\:border-blue-500:focus {
  border-color: hsl(var(--primary) / 0.5);
}

/* Smooth transitions */
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Responsive grid improvements */
@media (min-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .bg-white {
    background-color: white !important;
  }
  
  .text-gray-900 {
    color: black !important;
  }
}
