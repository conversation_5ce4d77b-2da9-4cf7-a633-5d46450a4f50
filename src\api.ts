import type { PlacesSearchResponse, AppConfig } from './types.js';

// Configuration - WARNING: This key is publicly visible. Please restrict it in your Google Cloud Console.
const config: AppConfig = {
  apiKey: 'AIzaSyB-kl4FwEBDnULVS-5FzgqmOILaHs5AETc',
  placesApiUrl: 'https://maps.googleapis.com/maps/api/place/textsearch/json'
};

/**
 * Searches for places using the Google Places API
 * @param query - The search query
 * @returns Promise resolving to the API response
 */
export async function searchPlaces(query: string): Promise<PlacesSearchResponse> {
  if (!query.trim()) {
    throw new Error('Search query cannot be empty');
  }

  // Append "in USA" to prioritize US-based results
  const fullQuery = `${query.trim()} in USA`;
  
  // Construct the API URL
  const requestUrl = new URL(config.placesApiUrl);
  requestUrl.searchParams.set('query', fullQuery);
  requestUrl.searchParams.set('key', config.apiKey);

  try {
    const response = await fetch(requestUrl.toString());

    if (!response.ok) {
      throw new Error(`API request failed with status: ${response.status} ${response.statusText}`);
    }

    const data: PlacesSearchResponse = await response.json();

    // Handle Google API specific errors
    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
      const errorMessage = data.error_message || `API Error: ${data.status}`;
      throw new Error(errorMessage);
    }

    return data;

  } catch (error) {
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      // This is likely a CORS error
      throw new Error(
        'Search failed due to CORS restrictions. Please ensure you are running this from a web server ' +
        '(not a local file) and that your API key is properly restricted with the correct HTTP referrer ' +
        'in the Google Cloud Console.'
      );
    }
    
    // Re-throw other errors as-is
    throw error;
  }
}

/**
 * Gets the current API configuration
 * @returns The current configuration
 */
export function getConfig(): AppConfig {
  return { ...config };
}

/**
 * Updates the API key (useful for configuration)
 * @param newApiKey - The new API key to use
 */
export function setApiKey(newApiKey: string): void {
  if (!newApiKey.trim()) {
    throw new Error('API key cannot be empty');
  }
  config.apiKey = newApiKey.trim();
}
