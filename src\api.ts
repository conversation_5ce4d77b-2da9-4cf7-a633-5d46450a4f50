import type { PlacesSearchResponse, AppConfig, Place } from './types.js';

// Declare Google Maps API types
declare global {
  interface Window {
    google: any;
  }
}

// Configuration
const config: AppConfig = {
  apiKey: 'AIzaSyB-kl4FwEBDnULVS-5FzgqmOILaHs5AETc',
  placesApiUrl: '' // Not used with Maps JavaScript API
};

/**
 * Waits for Google Maps API to be loaded
 */
function waitForGoogleMaps(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (window.google && window.google.maps && window.google.maps.places) {
      resolve();
      return;
    }

    const checkInterval = setInterval(() => {
      if (window.google && window.google.maps && window.google.maps.places) {
        clearInterval(checkInterval);
        resolve();
      }
    }, 100);

    // Timeout after 10 seconds
    setTimeout(() => {
      clearInterval(checkInterval);
      reject(new Error('Google Maps API failed to load within 10 seconds'));
    }, 10000);
  });
}

/**
 * Searches for places using the Google Maps JavaScript API
 * @param query - The search query
 * @returns Promise resolving to the API response
 */
export async function searchPlaces(query: string): Promise<PlacesSearchResponse> {
  if (!query.trim()) {
    throw new Error('Search query cannot be empty');
  }

  try {
    // Wait for Google Maps API to be loaded
    await waitForGoogleMaps();

    // Append "in USA" to prioritize US-based results
    const fullQuery = `${query.trim()} in USA`;

    return new Promise((resolve, reject) => {
      // Create a hidden div for the map (required by PlacesService)
      const mapDiv = document.createElement('div');
      mapDiv.style.display = 'none';
      document.body.appendChild(mapDiv);

      // Create a map instance (required by PlacesService)
      const map = new window.google.maps.Map(mapDiv);

      // Create PlacesService instance
      const service = new window.google.maps.places.PlacesService(map);

      // Perform text search
      service.textSearch(
        {
          query: fullQuery,
        },
        (results: any, status: any) => {
          // Clean up the hidden map div
          document.body.removeChild(mapDiv);

          if (status === window.google.maps.places.PlacesServiceStatus.OK && results) {
            // Convert Google Maps API results to our expected format
            const convertedResults: Place[] = results.map(convertGooglePlaceToPlace);

            resolve({
              html_attributions: [],
              results: convertedResults,
              status: 'OK'
            });
          } else if (status === window.google.maps.places.PlacesServiceStatus.ZERO_RESULTS) {
            resolve({
              html_attributions: [],
              results: [],
              status: 'ZERO_RESULTS'
            });
          } else {
            reject(new Error(`Places API Error: ${status}`));
          }
        }
      );
    });

  } catch (error) {
    throw error;
  }
}

/**
 * Converts Google Maps API place result to our Place interface
 */
function convertGooglePlaceToPlace(googlePlace: any): Place {
  return {
    business_status: googlePlace.business_status,
    formatted_address: googlePlace.formatted_address,
    geometry: {
      location: {
        lat: googlePlace.geometry?.location?.lat() || 0,
        lng: googlePlace.geometry?.location?.lng() || 0,
      },
      viewport: googlePlace.geometry?.viewport ? {
        northeast: {
          lat: googlePlace.geometry.viewport.getNorthEast().lat(),
          lng: googlePlace.geometry.viewport.getNorthEast().lng(),
        },
        southwest: {
          lat: googlePlace.geometry.viewport.getSouthWest().lat(),
          lng: googlePlace.geometry.viewport.getSouthWest().lng(),
        },
      } : undefined,
    },
    icon: googlePlace.icon,
    icon_background_color: googlePlace.icon_background_color,
    icon_mask_base_uri: googlePlace.icon_mask_base_uri,
    name: googlePlace.name || '',
    opening_hours: googlePlace.opening_hours ? {
      open_now: googlePlace.opening_hours.open_now || false,
      periods: googlePlace.opening_hours.periods,
      weekday_text: googlePlace.opening_hours.weekday_text,
    } : undefined,
    photos: googlePlace.photos?.map((photo: any) => ({
      height: photo.height,
      html_attributions: photo.html_attributions || [],
      photo_reference: photo.getUrl ? photo.getUrl() : '',
      width: photo.width,
    })),
    place_id: googlePlace.place_id || '',
    plus_code: googlePlace.plus_code,
    price_level: googlePlace.price_level,
    rating: googlePlace.rating,
    reference: googlePlace.reference,
    types: googlePlace.types || [],
    user_ratings_total: googlePlace.user_ratings_total,
  };
}

/**
 * Gets the current API configuration
 * @returns The current configuration
 */
export function getConfig(): AppConfig {
  return { ...config };
}

/**
 * Updates the API key (useful for configuration)
 * @param newApiKey - The new API key to use
 */
export function setApiKey(newApiKey: string): void {
  if (!newApiKey.trim()) {
    throw new Error('API key cannot be empty');
  }
  config.apiKey = newApiKey.trim();
}
