import type { PlacesSearchResponse, AppConfig, Place, PlaceDetails } from './types.js';

// Declare Google Maps API types
declare global {
  interface Window {
    google: any;
  }
}

// Configuration
const config: AppConfig = {
  apiKey: 'AIzaSyB-kl4FwEBDnULVS-5FzgqmOILaHs5AETc',
  placesApiUrl: '' // Not used with Maps JavaScript API
};

/**
 * Waits for Google Maps API to be loaded
 */
function waitForGoogleMaps(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (window.google && window.google.maps && window.google.maps.places) {
      resolve();
      return;
    }

    const checkInterval = setInterval(() => {
      if (window.google && window.google.maps && window.google.maps.places) {
        clearInterval(checkInterval);
        resolve();
      }
    }, 100);

    // Timeout after 10 seconds
    setTimeout(() => {
      clearInterval(checkInterval);
      reject(new Error('Google Maps API failed to load within 10 seconds'));
    }, 10000);
  });
}

/**
 * Searches for places using the Google Maps JavaScript API and enriches with details
 * @param query - The search query
 * @param maxResults - Maximum number of results to return
 * @param onProgress - Optional callback for progress updates
 * @returns Promise resolving to the API response with enriched place data
 */
export async function searchPlaces(
  query: string,
  maxResults: number = 20,
  onProgress?: (current: number, total: number, stage?: string) => void
): Promise<PlacesSearchResponse> {
  if (!query.trim()) {
    throw new Error('Search query cannot be empty');
  }

  if (maxResults < 1) {
    throw new Error('Max results must be at least 1');
  }

  try {
    // Wait for Google Maps API to be loaded
    await waitForGoogleMaps();

    // Append "in USA" to prioritize US-based results
    const fullQuery = `${query.trim()} in USA`;

    return new Promise((resolve, reject) => {
      const allResults: Place[] = [];
      let searchCount = 0;
      const maxSearches = Math.ceil(maxResults / 20); // Google returns max 20 per request

      // Create a hidden div for the map (required by PlacesService)
      const mapDiv = document.createElement('div');
      mapDiv.style.display = 'none';
      document.body.appendChild(mapDiv);

      // Create a map instance (required by PlacesService)
      const map = new window.google.maps.Map(mapDiv);

      // Create PlacesService instance
      const service = new window.google.maps.places.PlacesService(map);

      function performSearch(pageToken?: string) {
        const searchRequest: any = {
          query: fullQuery,
        };

        if (pageToken) {
          searchRequest.pageToken = pageToken;
        }

        service.textSearch(searchRequest, (results: any, status: any, pagination: any) => {
          searchCount++;

          if (status === window.google.maps.places.PlacesServiceStatus.OK && results) {
            // Convert and add results
            const convertedResults: Place[] = results.map(convertGooglePlaceToPlace);
            allResults.push(...convertedResults);

            // Call progress callback if provided
            if (onProgress) {
              onProgress(allResults.length, maxResults);
            }

            // Check if we have enough results or if there are more pages
            const hasMorePages = pagination && pagination.hasNextPage;
            const needMoreResults = allResults.length < maxResults;
            const canSearchMore = searchCount < maxSearches;

            if (needMoreResults && hasMorePages && canSearchMore) {
              // Wait a bit before next request (required by Google API)
              setTimeout(() => {
                pagination.nextPage();
              }, 2000);
            } else {
              // We're done searching - now enrich with place details
              document.body.removeChild(mapDiv);

              // Limit results to requested maximum
              const finalResults = allResults.slice(0, maxResults);

              // Enrich results with place details
              enrichResultsWithDetails(finalResults, onProgress)
                .then(enrichedResults => {
                  resolve({
                    html_attributions: [],
                    results: enrichedResults,
                    status: 'OK'
                  });
                })
                .catch(error => {
                  console.warn('Failed to enrich some results with details:', error);
                  // Return original results if enrichment fails
                  resolve({
                    html_attributions: [],
                    results: finalResults,
                    status: 'OK'
                  });
                });
            }
          } else if (status === window.google.maps.places.PlacesServiceStatus.ZERO_RESULTS) {
            document.body.removeChild(mapDiv);
            resolve({
              html_attributions: [],
              results: allResults, // Return any results we got so far
              status: allResults.length > 0 ? 'OK' : 'ZERO_RESULTS'
            });
          } else {
            document.body.removeChild(mapDiv);
            if (allResults.length > 0) {
              // Return partial results if we have some
              resolve({
                html_attributions: [],
                results: allResults.slice(0, maxResults),
                status: 'OK'
              });
            } else {
              reject(new Error(`Places API Error: ${status}`));
            }
          }
        });
      }

      // Start the search
      performSearch();
    });

  } catch (error) {
    throw error;
  }
}

/**
 * Converts Google Maps API place result to our Place interface
 */
function convertGooglePlaceToPlace(googlePlace: any): Place {
  return {
    business_status: googlePlace.business_status,
    formatted_address: googlePlace.formatted_address,
    geometry: {
      location: {
        lat: googlePlace.geometry?.location?.lat() || 0,
        lng: googlePlace.geometry?.location?.lng() || 0,
      },
      viewport: googlePlace.geometry?.viewport ? {
        northeast: {
          lat: googlePlace.geometry.viewport.getNorthEast().lat(),
          lng: googlePlace.geometry.viewport.getNorthEast().lng(),
        },
        southwest: {
          lat: googlePlace.geometry.viewport.getSouthWest().lat(),
          lng: googlePlace.geometry.viewport.getSouthWest().lng(),
        },
      } : undefined,
    },
    icon: googlePlace.icon,
    icon_background_color: googlePlace.icon_background_color,
    icon_mask_base_uri: googlePlace.icon_mask_base_uri,
    name: googlePlace.name || '',
    opening_hours: googlePlace.opening_hours ? {
      open_now: googlePlace.opening_hours.open_now || false,
      periods: googlePlace.opening_hours.periods,
      weekday_text: googlePlace.opening_hours.weekday_text,
    } : undefined,
    photos: googlePlace.photos?.map((photo: any) => ({
      height: photo.height,
      html_attributions: photo.html_attributions || [],
      photo_reference: photo.getUrl ? photo.getUrl() : '',
      width: photo.width,
    })),
    place_id: googlePlace.place_id || '',
    plus_code: googlePlace.plus_code,
    price_level: googlePlace.price_level,
    rating: googlePlace.rating,
    reference: googlePlace.reference,
    types: googlePlace.types || [],
    user_ratings_total: googlePlace.user_ratings_total,
  };
}

/**
 * Enriches search results with detailed information from Place Details API
 * @param places - Array of places to enrich
 * @param onProgress - Optional progress callback
 * @returns Promise resolving to enriched places
 */
async function enrichResultsWithDetails(
  places: Place[],
  onProgress?: (current: number, total: number, stage?: string) => void
): Promise<Place[]> {
  const enrichedPlaces: Place[] = [];

  for (let i = 0; i < places.length; i++) {
    const place = places[i];

    // Update progress
    if (onProgress) {
      onProgress(i + 1, places.length, 'Fetching contact details');
    }

    try {
      // Fetch place details
      const details = await getPlaceDetails(place.place_id);

      // Merge details into place object
      const enrichedPlace: Place = {
        ...place,
        // Add details data to the place object for later use
        _details: details
      } as any;

      enrichedPlaces.push(enrichedPlace);

      // Small delay to avoid hitting rate limits
      if (i < places.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

    } catch (error) {
      console.warn(`Failed to fetch details for ${place.name}:`, error);
      // Add place without details
      enrichedPlaces.push(place);
    }
  }

  return enrichedPlaces;
}

/**
 * Fetches detailed information for a place using Place Details API
 * @param placeId - The Google Place ID
 * @returns Promise resolving to place details
 */
export async function getPlaceDetails(placeId: string): Promise<PlaceDetails | null> {
  try {
    await waitForGoogleMaps();

    return new Promise((resolve, reject) => {
      // Create a hidden div for the map (required by PlacesService)
      const mapDiv = document.createElement('div');
      mapDiv.style.display = 'none';
      document.body.appendChild(mapDiv);

      // Create a map instance (required by PlacesService)
      const map = new window.google.maps.Map(mapDiv);

      // Create PlacesService instance
      const service = new window.google.maps.places.PlacesService(map);

      // Request place details
      service.getDetails(
        {
          placeId: placeId,
          fields: [
            'place_id',
            'formatted_phone_number',
            'international_phone_number',
            'website',
            'url',
            'opening_hours',
            'photos',
            'reviews'
          ]
        },
        (place: any, status: any) => {
          // Clean up the hidden map div
          document.body.removeChild(mapDiv);

          if (status === window.google.maps.places.PlacesServiceStatus.OK && place) {
            const details: PlaceDetails = {
              place_id: place.place_id,
              formatted_phone_number: place.formatted_phone_number,
              international_phone_number: place.international_phone_number,
              website: place.website,
              url: place.url,
              opening_hours: place.opening_hours ? {
                open_now: place.opening_hours.open_now,
                periods: place.opening_hours.periods,
                weekday_text: place.opening_hours.weekday_text,
              } : undefined,
              photos: place.photos?.map((photo: any) => ({
                height: photo.height,
                width: photo.width,
                photo_reference: photo.photo_reference,
                html_attributions: photo.html_attributions || [],
              })),
              reviews: place.reviews?.map((review: any) => ({
                author_name: review.author_name,
                author_url: review.author_url,
                language: review.language,
                profile_photo_url: review.profile_photo_url,
                rating: review.rating,
                relative_time_description: review.relative_time_description,
                text: review.text,
                time: review.time,
              })) || [],
            };
            resolve(details);
          } else {
            // Don't reject on details failure, just return null
            resolve(null);
          }
        }
      );
    });

  } catch (error) {
    console.warn('Failed to fetch place details:', error);
    return null;
  }
}

/**
 * Gets the current API configuration
 * @returns The current configuration
 */
export function getConfig(): AppConfig {
  return { ...config };
}

/**
 * Updates the API key (useful for configuration)
 * @param newApiKey - The new API key to use
 */
export function setApiKey(newApiKey: string): void {
  if (!newApiKey.trim()) {
    throw new Error('API key cannot be empty');
  }
  config.apiKey = newApiKey.trim();
}
