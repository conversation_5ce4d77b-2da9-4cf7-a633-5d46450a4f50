# Google Places Lead Generator

A modern TypeScript application for generating business leads using the Google Places API. This tool allows you to search for businesses by type and location, view detailed information, and export results to CSV or JSON formats.

## Features

- 🔍 **Smart Search**: Search for businesses by type and location with automatic US filtering
- 📊 **Rich Results**: View business details including ratings, reviews, hours, and contact info
- 📤 **Export Options**: Export results to CSV or JSON formats
- 📱 **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- ⚡ **Fast & Modern**: Built with Vite, TypeScript, and Tailwind CSS
- 🛡️ **Type Safe**: Full TypeScript coverage for better development experience
- ♿ **Accessible**: Keyboard shortcuts and screen reader friendly

## Prerequisites

- Node.js 18+ 
- A Google Cloud Platform account with Places API enabled
- Google Places API key (see setup instructions below)

## Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd google-places-lead-generator
   npm install
   ```

2. **Configure API Key**
   - Edit `src/api.ts` and replace the API key with your own
   - Or set it programmatically using the `setApiKey()` function

3. **Start Development Server**
   ```bash
   npm run dev
   ```

4. **Open in Browser**
   - Navigate to `http://localhost:3000`
   - Start searching for business leads!

## Google API Setup

### 1. Create a Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the **Places API** in the API Library

### 2. Create API Key
1. Go to **Credentials** in the Google Cloud Console
2. Click **Create Credentials** → **API Key**
3. Copy your API key

### 3. Restrict API Key (Important!)
1. Click on your API key to edit it
2. Under **Application restrictions**, select **HTTP referrers**
3. Add your domain(s):
   - For local development: `http://localhost:3000/*`
   - For production: `https://yourdomain.com/*`
4. Under **API restrictions**, select **Restrict key** and choose **Places API**

### 4. Update Configuration
Replace the API key in `src/api.ts`:
```typescript
const config: AppConfig = {
  apiKey: 'YOUR_API_KEY_HERE',
  placesApiUrl: 'https://maps.googleapis.com/maps/api/place/textsearch/json'
};
```

## Usage

### Basic Search
1. Enter a search query like "Pressure Washing Phoenix AZ"
2. Click **Search** or press Enter
3. View results in the grid layout

### Export Results
- **CSV Export**: Click "Export to CSV" for spreadsheet-compatible format
- **JSON Export**: Click "Export to JSON" for structured data format

### Keyboard Shortcuts
- `Ctrl/Cmd + K`: Focus search input
- `Escape`: Clear search focus
- `Enter`: Submit search

## Project Structure

```
├── src/
│   ├── main.ts          # Application entry point
│   ├── api.ts           # Google Places API integration
│   ├── ui.ts            # UI manipulation and rendering
│   ├── utils.ts         # Utility functions
│   ├── types.ts         # TypeScript type definitions
│   └── style.css        # Custom styles and Tailwind imports
├── index.html           # Main HTML template
├── package.json         # Dependencies and scripts
├── tsconfig.json        # TypeScript configuration
├── vite.config.ts       # Vite build configuration
├── tailwind.config.js   # Tailwind CSS configuration
└── postcss.config.js    # PostCSS configuration
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run type-check` - Run TypeScript type checking

## API Response Format

The application processes Google Places API responses and exports data in this format:

```typescript
interface ExportData {
  name: string;           // Business name
  address: string;        // Formatted address
  rating: number;         // Average rating (0-5)
  totalRatings: number;   // Number of reviews
  status: string;         // Open/Closed/Hours unavailable
  placeId: string;        // Google Place ID
  types: string[];        // Business categories
}
```

## Troubleshooting

### CORS Errors
- Ensure you're running from a web server (not `file://`)
- Check that your API key has proper HTTP referrer restrictions
- Verify the referrer URL matches your development/production domain

### No Results
- Try broader search terms
- Check if your API key has sufficient quota
- Verify the Places API is enabled in Google Cloud Console

### Build Issues
- Ensure Node.js 18+ is installed
- Clear `node_modules` and reinstall: `rm -rf node_modules package-lock.json && npm install`
- Check TypeScript errors: `npm run type-check`

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes with proper TypeScript types
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Security Notes

- Never commit API keys to version control
- Always restrict API keys in Google Cloud Console
- Use environment variables for production deployments
- Regularly rotate API keys for security
