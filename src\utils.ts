import type { Place, ExportData } from './types.js';

/**
 * Converts a Place object to ExportData format
 * @param place - The place object from Google Places API
 * @returns Formatted export data
 */
export function placeToExportData(place: Place): ExportData {
  return {
    name: place.name || 'N/A',
    address: place.formatted_address || 'No address provided',
    rating: place.rating || 0,
    totalRatings: place.user_ratings_total || 0,
    status: getPlaceStatus(place),
    placeId: place.place_id,
    types: place.types || []
  };
}

/**
 * Gets the status string for a place (Open/Closed/Unknown)
 * @param place - The place object
 * @returns Status string
 */
export function getPlaceStatus(place: Place): string {
  if (!place.opening_hours) {
    return 'Hours unavailable';
  }
  return place.opening_hours.open_now ? 'Open' : 'Closed';
}

/**
 * Generates star rating display string
 * @param rating - Numeric rating (0-5)
 * @returns String with filled and empty stars
 */
export function generateStarRating(rating: number): string {
  const filledStars = Math.round(rating);
  const emptyStars = 5 - filledStars;
  return '★'.repeat(filledStars) + '☆'.repeat(emptyStars);
}

/**
 * Converts array of export data to CSV format
 * @param data - Array of export data
 * @returns CSV string
 */
export function convertToCSV(data: ExportData[]): string {
  if (data.length === 0) {
    return '';
  }

  const headers = ['Name', 'Address', 'Rating', 'Total Ratings', 'Status', 'Place ID', 'Types'];
  
  const rows = data.map(item => [
    `"${item.name.replace(/"/g, '""')}"`,
    `"${item.address.replace(/"/g, '""')}"`,
    item.rating.toString(),
    item.totalRatings.toString(),
    `"${item.status.replace(/"/g, '""')}"`,
    `"${item.placeId.replace(/"/g, '""')}"`,
    `"${item.types.join(', ').replace(/"/g, '""')}"`
  ]);

  return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
}

/**
 * Triggers a file download in the browser
 * @param content - The content of the file
 * @param fileName - The name of the file
 * @param contentType - The MIME type of the file
 */
export function downloadFile(content: string, fileName: string, contentType: string): void {
  const blob = new Blob([content], { type: contentType });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the URL object
  URL.revokeObjectURL(url);
}

/**
 * Generates a Google Maps search URL for a place
 * @param name - Place name
 * @param address - Place address
 * @returns Google Maps URL
 */
export function generateMapsUrl(name: string, address: string): string {
  const query = encodeURIComponent(`${name} ${address}`);
  return `https://www.google.com/maps/search/?api=1&query=${query}`;
}

/**
 * Debounces a function call
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout>;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
