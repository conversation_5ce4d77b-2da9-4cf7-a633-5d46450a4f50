import type { Place, ExportData, PlaceDetails } from './types.js';

/**
 * Converts a Place object to ExportData format
 * @param place - The place object from Google Places API
 * @param details - Optional place details from Place Details API
 * @returns Formatted export data
 */
export function placeToExportData(place: Place, details?: PlaceDetails | null): ExportData {
  return {
    name: place.name || 'N/A',
    address: place.formatted_address || 'No address provided',
    rating: place.rating || 0,
    totalRatings: place.user_ratings_total || 0,
    status: getPlaceStatus(place),
    placeId: place.place_id,
    types: place.types || [],
    // Location data
    latitude: place.geometry?.location?.lat || 0,
    longitude: place.geometry?.location?.lng || 0,
    plusCode: place.plus_code?.global_code,
    // Business details
    businessStatus: place.business_status,
    priceLevel: place.price_level,
    priceLevelText: getPriceLevelText(place.price_level),
    // Contact information (from Place Details API)
    phoneNumber: details?.formatted_phone_number,
    internationalPhoneNumber: details?.international_phone_number,
    website: details?.website,
    googleMapsUrl: details?.url || generateMapsUrl(place.name || '', place.formatted_address || ''),
    // Hours information
    weekdayText: details?.opening_hours?.weekday_text || place.opening_hours?.weekday_text,
    // Visual content
    photoUrls: getPhotoUrls(details?.photos || place.photos),
    iconUrl: place.icon,
    // Reviews
    reviews: details?.reviews || [],
    recentReviewsText: getRecentReviewsText(details?.reviews),
    averageRecentRating: getAverageRecentRating(details?.reviews),
  };
}

/**
 * Gets the status string for a place (Open/Closed/Unknown)
 * @param place - The place object
 * @returns Status string
 */
export function getPlaceStatus(place: Place): string {
  if (!place.opening_hours) {
    return 'Hours unavailable';
  }
  return place.opening_hours.open_now ? 'Open' : 'Closed';
}

/**
 * Converts price level number to descriptive text
 * @param priceLevel - Price level (0-4)
 * @returns Descriptive text
 */
export function getPriceLevelText(priceLevel?: number): string {
  if (priceLevel === undefined || priceLevel === null) return 'Unknown';

  switch (priceLevel) {
    case 0: return 'Free';
    case 1: return 'Inexpensive';
    case 2: return 'Moderate';
    case 3: return 'Expensive';
    case 4: return 'Very Expensive';
    default: return 'Unknown';
  }
}

/**
 * Extracts photo URLs from photo objects
 * @param photos - Array of photo objects
 * @returns Array of photo URLs
 */
export function getPhotoUrls(photos?: any[]): string[] {
  if (!photos || photos.length === 0) return [];

  return photos.slice(0, 5).map(photo => {
    // For Place Details API photos
    if (photo.photo_reference) {
      return `https://maps.googleapis.com/maps/api/place/photo?maxwidth=400&photoreference=${photo.photo_reference}&key=AIzaSyB-kl4FwEBDnULVS-5FzgqmOILaHs5AETc`;
    }
    // For Text Search API photos (already have getUrl method)
    if (photo.getUrl) {
      return photo.getUrl({ maxWidth: 400 });
    }
    // Fallback
    return photo.photo_reference || '';
  }).filter(url => url);
}

/**
 * Processes recent reviews into a readable text summary
 * @param reviews - Array of reviews
 * @returns Formatted text summary of recent reviews
 */
export function getRecentReviewsText(reviews?: any[]): string {
  if (!reviews || reviews.length === 0) return '';

  // Take the most recent 10 reviews and format them
  const recentReviews = reviews.slice(0, 10);

  return recentReviews.map(review => {
    const stars = '★'.repeat(review.rating) + '☆'.repeat(5 - review.rating);
    const text = review.text.length > 200 ? review.text.substring(0, 200) + '...' : review.text;
    return `${stars} ${review.author_name} (${review.relative_time_description}): ${text}`;
  }).join('\n\n');
}

/**
 * Calculates average rating from recent reviews
 * @param reviews - Array of reviews
 * @returns Average rating of recent reviews
 */
export function getAverageRecentRating(reviews?: any[]): number {
  if (!reviews || reviews.length === 0) return 0;

  // Calculate average of most recent 10 reviews
  const recentReviews = reviews.slice(0, 10);
  const totalRating = recentReviews.reduce((sum, review) => sum + review.rating, 0);

  return Math.round((totalRating / recentReviews.length) * 10) / 10; // Round to 1 decimal
}

/**
 * Generates star rating display string
 * @param rating - Numeric rating (0-5)
 * @returns String with filled and empty stars
 */
export function generateStarRating(rating: number): string {
  const filledStars = Math.round(rating);
  const emptyStars = 5 - filledStars;
  return '★'.repeat(filledStars) + '☆'.repeat(emptyStars);
}

/**
 * Converts array of export data to CSV format
 * @param data - Array of export data
 * @returns CSV string
 */
export function convertToCSV(data: ExportData[]): string {
  if (data.length === 0) {
    return '';
  }

  const headers = [
    'Name', 'Address', 'Phone Number', 'International Phone', 'Website',
    'Google Maps URL', 'Rating', 'Total Ratings', 'Status', 'Business Status',
    'Price Level', 'Price Level Text', 'Latitude', 'Longitude', 'Plus Code',
    'Place ID', 'Types', 'Hours', 'Photo URLs', 'Icon URL',
    'Recent Reviews Count', 'Average Recent Rating', 'Recent Reviews Text'
  ];

  const rows = data.map(item => [
    `"${item.name.replace(/"/g, '""')}"`,
    `"${item.address.replace(/"/g, '""')}"`,
    `"${(item.phoneNumber || '').replace(/"/g, '""')}"`,
    `"${(item.internationalPhoneNumber || '').replace(/"/g, '""')}"`,
    `"${(item.website || '').replace(/"/g, '""')}"`,
    `"${(item.googleMapsUrl || '').replace(/"/g, '""')}"`,
    item.rating.toString(),
    item.totalRatings.toString(),
    `"${item.status.replace(/"/g, '""')}"`,
    `"${(item.businessStatus || '').replace(/"/g, '""')}"`,
    (item.priceLevel !== undefined ? item.priceLevel.toString() : ''),
    `"${(item.priceLevelText || '').replace(/"/g, '""')}"`,
    item.latitude.toString(),
    item.longitude.toString(),
    `"${(item.plusCode || '').replace(/"/g, '""')}"`,
    `"${item.placeId.replace(/"/g, '""')}"`,
    `"${item.types.join(', ').replace(/"/g, '""')}"`,
    `"${(item.weekdayText?.join('; ') || '').replace(/"/g, '""')}"`,
    `"${(item.photoUrls?.join('; ') || '').replace(/"/g, '""')}"`,
    `"${(item.iconUrl || '').replace(/"/g, '""')}"`,
    (item.reviews?.length || 0).toString(),
    (item.averageRecentRating || 0).toString(),
    `"${(item.recentReviewsText || '').replace(/"/g, '""')}"`
  ]);

  return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
}

/**
 * Triggers a file download in the browser
 * @param content - The content of the file
 * @param fileName - The name of the file
 * @param contentType - The MIME type of the file
 */
export function downloadFile(content: string, fileName: string, contentType: string): void {
  const blob = new Blob([content], { type: contentType });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the URL object
  URL.revokeObjectURL(url);
}

/**
 * Generates a Google Maps search URL for a place
 * @param name - Place name
 * @param address - Place address
 * @returns Google Maps URL
 */
export function generateMapsUrl(name: string, address: string): string {
  const query = encodeURIComponent(`${name} ${address}`);
  return `https://www.google.com/maps/search/?api=1&query=${query}`;
}

/**
 * Debounces a function call
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout>;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
