import type { Place, MessageType, ExportData } from './types.js';
import { 
  placeToExportData, 
  generateStarRating, 
  generateMapsUrl, 
  convertToCSV, 
  downloadFile 
} from './utils.js';

// DOM element references
const elements = {
  statusArea: document.getElementById('status-area') as HTMLDivElement,
  exportControls: document.getElementById('export-controls') as HTMLDivElement,
  resultsGrid: document.getElementById('results-grid') as HTMLDivElement,
  exportCsvButton: document.getElementById('export-csv') as HTMLButtonElement,
  exportJsonButton: document.getElementById('export-json') as HTMLButtonElement,
};

// State
let currentResults: Place[] = [];

/**
 * Displays search results in the grid
 * @param places - Array of places to display
 */
export function displayResults(places: Place[]): void {
  currentResults = places;
  
  if (places.length === 0) {
    displayMessage('No results found.', 'info');
    return;
  }

  // Clear previous results
  elements.resultsGrid.innerHTML = '';
  
  // Show export controls
  elements.exportControls.classList.remove('hidden');

  // Create place cards
  places.forEach(place => {
    const placeCard = createPlaceCard(place);
    elements.resultsGrid.appendChild(placeCard);
  });
}

/**
 * Creates a place card element
 * @param place - Place data
 * @returns HTML element for the place card
 */
function createPlaceCard(place: Place): HTMLDivElement {
  const card = document.createElement('div');
  card.className = 'bg-neutral-900 border border-neutral-800 p-6 rounded-lg card-hover-blue flex flex-col gap-4';

  // Extract details if available (added during enrichment)
  const details = (place as any)._details;
  const exportData = placeToExportData(place, details);
  const starRating = generateStarRating(exportData.rating);
  const mapsUrl = exportData.googleMapsUrl || generateMapsUrl(exportData.name, exportData.address);

  const statusColor = exportData.status === 'Open' ? 'text-green-400' :
                     exportData.status === 'Closed' ? 'text-red-400' : 'text-neutral-400';

  // Create premium badge if it's a highly rated business
  const premiumBadge = exportData.rating >= 4.5 && exportData.totalRatings >= 50
    ? '<span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-gradient text-white glow-blue">Premium</span>'
    : '';

  card.innerHTML = `
    <div class="flex items-start justify-between">
      <h3 class="text-lg font-medium text-white flex-1 pr-2">${escapeHtml(exportData.name)}</h3>
      ${premiumBadge}
    </div>

    <div class="flex items-center gap-2 text-sm">
      <svg class="w-4 h-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 12.414a5.5 5.5 0 10-1.414 1.414l4.243 4.243a1 1 0 001.414-1.414z"></path>
      </svg>
      <p class="text-neutral-400 text-sm">${escapeHtml(exportData.address)}</p>
    </div>

    <div class="flex items-center gap-3">
      <div class="flex items-center gap-2">
        <span class="text-yellow-400 font-medium">${starRating}</span>
        <span class="text-neutral-500 text-sm">${exportData.rating.toFixed(1)}</span>
      </div>
      <span class="text-neutral-600">•</span>
      <span class="text-neutral-500 text-sm">${exportData.totalRatings} reviews</span>
    </div>

    <div class="flex items-center gap-2">
      <div class="w-2 h-2 rounded-full ${exportData.status === 'Open' ? 'bg-green-400' : exportData.status === 'Closed' ? 'bg-red-400' : 'bg-neutral-400'}"></div>
      <span class="text-sm font-medium ${statusColor}">${escapeHtml(exportData.status)}</span>
    </div>

    <div class="flex flex-wrap gap-1">
      ${exportData.types.slice(0, 3).map(type =>
        `<span class="inline-flex items-center px-2 py-1 rounded-md text-xs bg-neutral-800 text-neutral-400 border border-neutral-700">${escapeHtml(type.replace(/_/g, ' '))}</span>`
      ).join('')}
    </div>

    ${exportData.phoneNumber || exportData.website ? `
    <div class="border-t border-neutral-800 pt-4 space-y-2">
      ${exportData.phoneNumber ? `
        <div class="flex items-center gap-2 text-sm">
          <svg class="w-4 h-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
          </svg>
          <a href="tel:${exportData.phoneNumber}" class="text-blue-400 hover:text-blue-300 transition-colors">${escapeHtml(exportData.phoneNumber)}</a>
        </div>
      ` : ''}
      ${exportData.website ? `
        <div class="flex items-center gap-2 text-sm">
          <svg class="w-4 h-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0 9c-1.657 0-3-4.03-3-9s1.343-9 3-9m0 18c1.657 0 3-4.03 3-9s-1.343-9-3-9m-9 9a9 9 0 019-9"></path>
          </svg>
          <a href="${exportData.website}" target="_blank" rel="noopener noreferrer" class="text-blue-400 hover:text-blue-300 transition-colors">${escapeHtml(exportData.website.replace(/^https?:\/\//, ''))}</a>
        </div>
      ` : ''}
    </div>
    ` : ''}

    ${exportData.reviews && exportData.reviews.length > 0 ? `
    <div class="border-t border-neutral-800 pt-4">
      <h4 class="text-sm font-medium text-white mb-3 flex items-center gap-2">
        <svg class="w-4 h-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
        Recent Reviews (${exportData.reviews.length})
        ${exportData.averageRecentRating ? `
          <span class="text-yellow-400 text-xs">★ ${exportData.averageRecentRating}</span>
        ` : ''}
      </h4>
      <div class="space-y-3 max-h-32 overflow-y-auto">
        ${exportData.reviews.slice(0, 3).map(review => `
          <div class="bg-neutral-800 border border-neutral-700 p-3 rounded-lg">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center gap-2">
                <span class="text-yellow-400 text-sm">${'★'.repeat(review.rating)}${'☆'.repeat(5 - review.rating)}</span>
                <span class="text-neutral-400 text-xs">${escapeHtml(review.relative_time_description)}</span>
              </div>
              <span class="text-neutral-500 text-xs font-medium">${escapeHtml(review.author_name)}</span>
            </div>
            <p class="text-neutral-300 text-sm leading-relaxed">${escapeHtml(review.text.length > 150 ? review.text.substring(0, 150) + '...' : review.text)}</p>
          </div>
        `).join('')}
        ${exportData.reviews.length > 3 ? `
          <p class="text-neutral-500 text-xs text-center">+ ${exportData.reviews.length - 3} more reviews in export</p>
        ` : ''}
      </div>
    </div>
    ` : ''}

    <div class="flex gap-2 mt-auto pt-2">
      <a href="${mapsUrl}" target="_blank" rel="noopener noreferrer"
         class="flex-1 inline-flex items-center justify-center gap-2 text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors py-2 px-3 border border-blue-500/20 rounded-lg hover:bg-blue-500/10">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 12.414a5.5 5.5 0 10-1.414 1.414l4.243 4.243a1 1 0 001.414-1.414z"></path>
        </svg>
        View on Maps
      </a>
    </div>
  `;

  return card;
}

/**
 * Clears all results and status messages
 */
export function clearResults(): void {
  elements.resultsGrid.innerHTML = '';
  elements.statusArea.innerHTML = '';
  elements.exportControls.classList.add('hidden');
  currentResults = [];
}

/**
 * Shows or hides the loading indicator
 * @param isLoading - Whether to show the loader
 * @param message - Optional loading message
 */
export function showLoading(isLoading: boolean, message?: string): void {
  if (isLoading) {
    const loadingMessage = message || 'Searching for businesses...';
    elements.statusArea.innerHTML = `
      <div class="flex flex-col items-center gap-4 py-8">
        <div class="relative">
          <div class="loader w-12 h-12"></div>
          <div class="absolute inset-0 bg-blue-gradient rounded-full opacity-20 animate-pulse"></div>
        </div>
        <div class="text-center">
          <p class="text-white font-medium mb-1">${escapeHtml(loadingMessage)}</p>
          <p class="text-neutral-400 text-sm">Please wait while we fetch your leads...</p>
        </div>
      </div>
    `;
  } else {
    elements.statusArea.innerHTML = '';
  }
}

/**
 * Displays a status message to the user
 * @param message - The message to display
 * @param type - The type of message
 */
export function displayMessage(message: string, type: MessageType = 'info'): void {
  const getMessageConfig = (type: MessageType) => {
    switch (type) {
      case 'error':
        return {
          bgColor: 'bg-red-500/10',
          borderColor: 'border-red-500/20',
          textColor: 'text-red-400',
          icon: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                 </svg>`
        };
      case 'success':
        return {
          bgColor: 'bg-green-500/10',
          borderColor: 'border-green-500/20',
          textColor: 'text-green-400',
          icon: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                 </svg>`
        };
      default:
        return {
          bgColor: 'bg-blue-500/10',
          borderColor: 'border-blue-500/20',
          textColor: 'text-blue-400',
          icon: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                 </svg>`
        };
    }
  };

  const config = getMessageConfig(type);

  elements.statusArea.innerHTML = `
    <div class="max-w-md mx-auto ${config.bgColor} border ${config.borderColor} ${config.textColor} p-4 rounded-lg">
      <div class="flex items-center gap-3">
        ${config.icon}
        <p class="font-medium">${escapeHtml(message)}</p>
      </div>
    </div>
  `;
}

/**
 * Handles CSV export
 */
export function handleExportCsv(): void {
  if (currentResults.length === 0) {
    alert('No results to export.');
    return;
  }

  const exportData = currentResults.map(placeToExportData);
  const csvContent = convertToCSV(exportData);
  downloadFile(csvContent, 'leads.csv', 'text/csv;charset=utf-8;');
}

/**
 * Handles JSON export
 */
export function handleExportJson(): void {
  if (currentResults.length === 0) {
    alert('No results to export.');
    return;
  }

  const exportData = currentResults.map(placeToExportData);
  const jsonContent = JSON.stringify(exportData, null, 2);
  downloadFile(jsonContent, 'leads.json', 'application/json;charset=utf-8;');
}

/**
 * Sets up event listeners for export buttons
 */
export function setupExportListeners(): void {
  elements.exportCsvButton.addEventListener('click', handleExportCsv);
  elements.exportJsonButton.addEventListener('click', handleExportJson);
}

/**
 * Escapes HTML to prevent XSS
 * @param text - Text to escape
 * @returns Escaped text
 */
function escapeHtml(text: string): string {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}
