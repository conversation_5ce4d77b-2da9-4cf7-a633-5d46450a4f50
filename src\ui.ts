import type { Place, MessageType, ExportData } from './types.js';
import { 
  placeToExportData, 
  generateStarRating, 
  generateMapsUrl, 
  convertToCSV, 
  downloadFile 
} from './utils.js';

// DOM element references
const elements = {
  statusArea: document.getElementById('status-area') as HTMLDivElement,
  exportControls: document.getElementById('export-controls') as HTMLDivElement,
  resultsGrid: document.getElementById('results-grid') as HTMLDivElement,
  exportCsvButton: document.getElementById('export-csv') as HTMLButtonElement,
  exportJsonButton: document.getElementById('export-json') as HTMLButtonElement,
};

// State
let currentResults: Place[] = [];

/**
 * Displays search results in the grid
 * @param places - Array of places to display
 */
export function displayResults(places: Place[]): void {
  currentResults = places;
  
  if (places.length === 0) {
    displayMessage('No results found.', 'info');
    return;
  }

  // Clear previous results
  elements.resultsGrid.innerHTML = '';
  
  // Show export controls
  elements.exportControls.classList.remove('hidden');

  // Create place cards
  places.forEach(place => {
    const placeCard = createPlaceCard(place);
    elements.resultsGrid.appendChild(placeCard);
  });
}

/**
 * Creates a place card element
 * @param place - Place data
 * @returns HTML element for the place card
 */
function createPlaceCard(place: Place): HTMLDivElement {
  const card = document.createElement('div');
  card.className = 'bg-white p-5 rounded-lg shadow-md flex flex-col gap-3';
  
  const exportData = placeToExportData(place);
  const starRating = generateStarRating(exportData.rating);
  const mapsUrl = generateMapsUrl(exportData.name, exportData.address);
  
  const statusColor = exportData.status === 'Open' ? 'text-green-600' : 
                     exportData.status === 'Closed' ? 'text-red-600' : 'text-gray-600';

  card.innerHTML = `
    <h3 class="text-lg font-bold text-gray-900">${escapeHtml(exportData.name)}</h3>
    <p class="text-gray-600 text-sm">${escapeHtml(exportData.address)}</p>
    <div class="flex items-center gap-2 text-sm">
      <span class="font-semibold text-yellow-500">${starRating}</span>
      <span class="text-gray-500">${exportData.rating.toFixed(1)} (${exportData.totalRatings} reviews)</span>
    </div>
    <div class="text-sm font-semibold ${statusColor}">${escapeHtml(exportData.status)}</div>
    <div class="text-xs text-gray-500">
      <span class="font-medium">Types:</span> ${exportData.types.slice(0, 3).map(type => escapeHtml(type.replace(/_/g, ' '))).join(', ')}
    </div>
    <a href="${mapsUrl}" target="_blank" rel="noopener noreferrer" 
       class="text-indigo-600 hover:underline mt-auto pt-2 text-sm font-medium">
      View on Google Maps
    </a>
  `;

  return card;
}

/**
 * Clears all results and status messages
 */
export function clearResults(): void {
  elements.resultsGrid.innerHTML = '';
  elements.statusArea.innerHTML = '';
  elements.exportControls.classList.add('hidden');
  currentResults = [];
}

/**
 * Shows or hides the loading indicator
 * @param isLoading - Whether to show the loader
 * @param message - Optional loading message
 */
export function showLoading(isLoading: boolean, message?: string): void {
  if (isLoading) {
    const loadingMessage = message || 'Searching for businesses...';
    elements.statusArea.innerHTML = `
      <div class="flex flex-col items-center gap-3">
        <div class="loader border-4 border-gray-300 border-t-blue-500 rounded-full w-10 h-10 animate-spin"></div>
        <p class="text-gray-600 font-medium">${escapeHtml(loadingMessage)}</p>
      </div>
    `;
  } else {
    elements.statusArea.innerHTML = '';
  }
}

/**
 * Displays a status message to the user
 * @param message - The message to display
 * @param type - The type of message
 */
export function displayMessage(message: string, type: MessageType = 'info'): void {
  const colorClass = type === 'error' ? 'text-red-600' : 
                    type === 'success' ? 'text-green-600' : 'text-gray-600';
  
  elements.statusArea.innerHTML = `<p class="font-medium ${colorClass}">${escapeHtml(message)}</p>`;
}

/**
 * Handles CSV export
 */
export function handleExportCsv(): void {
  if (currentResults.length === 0) {
    alert('No results to export.');
    return;
  }

  const exportData = currentResults.map(placeToExportData);
  const csvContent = convertToCSV(exportData);
  downloadFile(csvContent, 'leads.csv', 'text/csv;charset=utf-8;');
}

/**
 * Handles JSON export
 */
export function handleExportJson(): void {
  if (currentResults.length === 0) {
    alert('No results to export.');
    return;
  }

  const exportData = currentResults.map(placeToExportData);
  const jsonContent = JSON.stringify(exportData, null, 2);
  downloadFile(jsonContent, 'leads.json', 'application/json;charset=utf-8;');
}

/**
 * Sets up event listeners for export buttons
 */
export function setupExportListeners(): void {
  elements.exportCsvButton.addEventListener('click', handleExportCsv);
  elements.exportJsonButton.addEventListener('click', handleExportJson);
}

/**
 * Escapes HTML to prevent XSS
 * @param text - Text to escape
 * @returns Escaped text
 */
function escapeHtml(text: string): string {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}
