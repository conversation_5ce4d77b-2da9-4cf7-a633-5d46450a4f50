import './style.css';
import { searchPlaces } from './api.js';
import { 
  displayResults, 
  clearResults, 
  showLoading, 
  displayMessage, 
  setupExportListeners 
} from './ui.js';
import { debounce } from './utils.js';
import type { SearchFormData } from './types.js';

/**
 * Main application class
 */
class LeadGeneratorApp {
  private searchForm: HTMLFormElement;
  private searchInput: HTMLInputElement;

  constructor() {
    // Get DOM elements
    this.searchForm = document.getElementById('search-form') as HTMLFormElement;
    this.searchInput = document.getElementById('search-query') as HTMLInputElement;

    if (!this.searchForm || !this.searchInput) {
      throw new Error('Required DOM elements not found');
    }

    this.init();
  }

  /**
   * Initialize the application
   */
  private init(): void {
    this.setupEventListeners();
    this.setupKeyboardShortcuts();
    
    // Focus on search input when page loads
    this.searchInput.focus();
    
    console.log('Google Places Lead Generator initialized');
  }

  /**
   * Set up event listeners
   */
  private setupEventListeners(): void {
    // Search form submission
    this.searchForm.addEventListener('submit', this.handleSearch.bind(this));
    
    // Setup export button listeners
    setupExportListeners();
    
    // Add input validation
    this.searchInput.addEventListener('input', this.handleInputChange.bind(this));
  }

  /**
   * Set up keyboard shortcuts
   */
  private setupKeyboardShortcuts(): void {
    document.addEventListener('keydown', (event) => {
      // Ctrl/Cmd + K to focus search
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        this.searchInput.focus();
        this.searchInput.select();
      }
      
      // Escape to clear search
      if (event.key === 'Escape') {
        this.searchInput.blur();
      }
    });
  }

  /**
   * Handle input changes with debounced validation
   */
  private handleInputChange = debounce((event: Event) => {
    const target = event.target as HTMLInputElement;
    const value = target.value.trim();
    
    // Basic validation feedback
    if (value.length > 0 && value.length < 3) {
      target.setCustomValidity('Search query must be at least 3 characters long');
    } else {
      target.setCustomValidity('');
    }
  }, 300);

  /**
   * Handle search form submission
   */
  private async handleSearch(event: Event): Promise<void> {
    event.preventDefault();
    
    const formData = new FormData(this.searchForm);
    const searchData: SearchFormData = {
      query: (formData.get('search-query') as string)?.trim() || ''
    };

    if (!searchData.query) {
      displayMessage('Please enter a search query.', 'error');
      this.searchInput.focus();
      return;
    }

    if (searchData.query.length < 3) {
      displayMessage('Search query must be at least 3 characters long.', 'error');
      this.searchInput.focus();
      return;
    }

    // Clear previous results and show loading
    clearResults();
    showLoading(true);

    try {
      console.log(`Searching for: ${searchData.query}`);
      
      const response = await searchPlaces(searchData.query);
      
      if (response.status === 'OK') {
        console.log(`Found ${response.results.length} results`);
        displayResults(response.results);
        
        if (response.results.length > 0) {
          displayMessage(`Found ${response.results.length} business${response.results.length === 1 ? '' : 'es'}`, 'success');
        }
      } else if (response.status === 'ZERO_RESULTS') {
        displayMessage('No results found for your query. Try different keywords or location.', 'info');
      } else {
        throw new Error(response.error_message || `API Error: ${response.status}`);
      }

    } catch (error) {
      console.error('Search failed:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      displayMessage(errorMessage, 'error');
      
      // Clear any partial results
      clearResults();
    } finally {
      showLoading(false);
    }
  }

  /**
   * Get current search query
   */
  public getCurrentQuery(): string {
    return this.searchInput.value.trim();
  }

  /**
   * Set search query programmatically
   */
  public setSearchQuery(query: string): void {
    this.searchInput.value = query;
  }
}

/**
 * Initialize the application when DOM is ready
 */
function initApp(): void {
  try {
    new LeadGeneratorApp();
  } catch (error) {
    console.error('Failed to initialize application:', error);
    
    // Show error message to user
    const statusArea = document.getElementById('status-area');
    if (statusArea) {
      statusArea.innerHTML = `
        <div class="text-red-600 font-medium">
          Failed to initialize application. Please refresh the page and try again.
        </div>
      `;
    }
  }
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp);
} else {
  initApp();
}
